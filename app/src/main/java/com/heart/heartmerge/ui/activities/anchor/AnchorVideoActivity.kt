package com.heart.heartmerge.ui.activities.anchor

import android.content.Intent
import android.media.MediaPlayer
import android.util.DisplayMetrics
import android.view.GestureDetector
import android.view.KeyEvent
import android.view.MotionEvent
import android.view.SurfaceView
import android.view.View
import android.view.View.OnTouchListener
import android.view.ViewGroup
import android.view.WindowManager
import androidx.activity.viewModels
import androidx.core.view.GestureDetectorCompat
import androidx.core.view.isGone
import androidx.core.view.isVisible
import com.bdc.android.library.base.activity.BaseCoreActivity
import com.bdc.android.library.bus.FlowBus
import com.bdc.android.library.extension.click
import com.bdc.android.library.extension.makeGone
import com.bdc.android.library.extension.makeVisible
import com.bdc.android.library.mvi.observeEvent
import com.bdc.android.library.mvi.observeState
import com.bdc.android.library.utils.ToastUtil
import com.heart.heartmerge.BuildConfig
import com.heart.heartmerge.R
import com.heart.heartmerge.beans.ChatTipBean
import com.heart.heartmerge.beans.GiftItemBean
import com.heart.heartmerge.beans.RemoteCallRefuseExtend
import com.heart.heartmerge.beans.UserBean
import com.heart.heartmerge.beans.VideoMessageBean
import com.heart.heartmerge.databinding.ActivityAnchorVideoBinding
import com.heart.heartmerge.extension.buildImageUrl
import com.heart.heartmerge.extension.loadAnchorImage
import com.heart.heartmerge.extension.loadAvatar
import com.heart.heartmerge.lce.BaseRequestEvent
import com.heart.heartmerge.logger.LogX
import com.heart.heartmerge.manager.DiamondChangeManager
import com.heart.heartmerge.manager.DiamondChangeManager.toShowDiamond
import com.heart.heartmerge.manager.FloatGiftManager
import com.heart.heartmerge.manager.GiftManager
import com.heart.heartmerge.mmkv.MMKVBaseDataRep
import com.heart.heartmerge.mmkv.MMKVDataRep
import com.heart.heartmerge.popup.DiamondRechargePopup
import com.heart.heartmerge.popup.NormalNewPopup
import com.heart.heartmerge.popup.showDiamondRechargePopup
import com.heart.heartmerge.popup.showGiftPopup
import com.heart.heartmerge.popup.showMembershipSubscribePopup
import com.heart.heartmerge.popup.showNormalNewPopup
import com.heart.heartmerge.popup.showReportPopup
import com.heart.heartmerge.socket.WebSocketManager
import com.heart.heartmerge.socket.WebSocketMessageSender
import com.heart.heartmerge.ui.fragments.home.VideoMessageItem
import com.heart.heartmerge.ui.widget.floatwindow.CallFloatWindow
import com.heart.heartmerge.utils.AppUtil
import com.heart.heartmerge.utils.Constants
import com.heart.heartmerge.utils.Constants.VIDEO_DEFAULT_EXIT_TIME
import com.heart.heartmerge.utils.PurchaseScene
import com.heart.heartmerge.utils.RongMessageUtil
import com.heart.heartmerge.utils.SoftInputUtil
import com.heart.heartmerge.utils.fromJson
import com.heart.heartmerge.utils.toJson
import com.heart.heartmerge.viewmodes.AnchorVideoPageState
import com.heart.heartmerge.viewmodes.AnchorVideoStatus
import com.heart.heartmerge.viewmodes.AnchorVideoViewModel
import com.heart.heartmerge.viewmodes.AnchorViewModel
import com.heart.heartmerge.viewmodes.SearchRequestEvent
import com.lxj.xpopup.XPopup
import com.lxj.xpopup.core.BasePopupView
import io.agora.rtc2.ChannelMediaOptions
import io.agora.rtc2.IRtcEngineEventHandler
import io.agora.rtc2.RtcEngine
import io.agora.rtc2.RtcEngineConfig
import io.agora.rtc2.video.BeautyOptions
import io.agora.rtc2.video.VideoCanvas
import io.rong.imkit.conversation.extension.parsemessage.MikChatAskGiftMessage
import io.rong.imlib.IRongCallback
import io.rong.imlib.RongIMClient
import io.rong.imlib.model.Conversation
import io.rong.imlib.model.Message
import io.rong.message.TextMessage
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch


/**
 * 作者：Lxf
 * 创建日期：2024/7/31 18:16
 * 描述：
 */
class AnchorVideoActivity : BaseCoreActivity<ActivityAnchorVideoBinding, AnchorVideoViewModel>() {
    private var anchorInfo: UserBean? = null

    private val source: Int
        get() = intent.getIntExtra(Constants.INTENT_PARAM_VIDEO_SOURCE, Constants.VIDEO_SOURCE_NORMAL)

    private val isNeedBal: Boolean
        get() = intent.getBooleanExtra(Constants.INTENT_PARAM_ISNEEDBAL, false)

    private val isIncomingCall: Boolean
        get() = intent.getBooleanExtra(Constants.INTENT_PARAM_IS_INCOMING_CALLING, false)

    private val recordId: Int get() = intent.getIntExtra(Constants.INTENT_PARAM_RECORD_ID, 0)

    private val anchorViewModel by viewModels<AnchorViewModel>()
    private var followed: Boolean = false //是否关注

    // 填写声网控制台中获取的 App ID
    private var channelId: String? = null
    private var mRtcEngine: RtcEngine? = null
    private var videoTimeJob: Job? = null //视频时长 每秒更新
    private var answerCountDownJob: Job? = null //接听倒计时
    private var joinChannelCountDownJob: Job? = null //加入房间倒计时
    private var rechargeCountDownJob: Job? = null//充值倒计时
    private var seconds = 0
    private var muteLocalVideoStream = false //true 取消发送本地视频流 false（默认）发送本地视频流
    private var muteLocalAudioStream = false //true取消发布 false（默认）发布
    private var exitTipPopupView: NormalNewPopup? = null
    private var localSelVideoCanvas: VideoCanvas? = null
    private var remoteVideoCanvas: VideoCanvas? = null
    private var isSelfPreview = true // 默认显示自己 当和主播接通之后切换为显示主播的视图
    private var anchorUid: Int = -1 //接通之后加入的主播ID
    private val timeString: StringBuilder = StringBuilder()
    private var giftPopup: BasePopupView? = null
    private var mediaPlayer: MediaPlayer? = null
    private var defaultAnchorDiamond = Constants.VIDEO_DEFAULT_PRICE
    private var diamondRechargePopup: DiamondRechargePopup? = null

    override fun getLayoutId(): Int = R.layout.activity_anchor_video
    override fun isImmerse(): Boolean = true
    private var answerEndTipStr: String = ""
    private var matchTipDialog: NormalNewPopup? = null
    private var callRejectDialog: NormalNewPopup? = null
    override fun initView() {
        if (!BuildConfig.DEBUG) {
            AppUtil.screenSecure(window)
        }
        answerEndTipStr = getString(R.string.tip_video_cancel)
        setAnchorInfoBgHeight()
        initViewShow()
        initActionView()
        window.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
        // 初始化GestureDetector
        val gestureDetector =
            GestureDetectorCompat(this, object : GestureDetector.SimpleOnGestureListener() {
                override fun onSingleTapUp(e: MotionEvent): Boolean {
                    isSelfPreview = checkIsSelfPreview()
                    switchVideoPreview()
                    return true
                }
            })
        mBinding.remoteVideoViewContainerFl.apply {
            setOnTouchListener(object : OnTouchListener {
                private var lastX = 0f
                private var lastY = 0f
                private var dX = 0f
                private var dY = 0f
                private var isDragging = false
                private val screenWidth = resources.displayMetrics.widthPixels
                private val screenHeight = resources.displayMetrics.heightPixels

                override fun onTouch(v: View, event: MotionEvent): Boolean {
                    when (event.action) {
                        MotionEvent.ACTION_DOWN -> {
                            // 记录手指按下时的坐标
                            lastX = event.rawX
                            lastY = event.rawY
                            dX = v.x - lastX
                            dY = v.y - lastY
                            isDragging = false
                        }

                        MotionEvent.ACTION_MOVE -> {
                            // 计算手指的移动距离并更新视图的位置
                            var newX = event.rawX + dX
                            var newY = event.rawY + dY

                            // 限制视图的移动范围
                            val viewWidth = v.width
                            val viewHeight = v.height

                            // 防止移动到屏幕外
                            newX = newX.coerceIn(0f, (screenWidth - viewWidth).toFloat())
                            newY = newY.coerceIn(0f, (screenHeight - viewHeight).toFloat())

                            // 更新View的位置
                            v.x = newX
                            v.y = newY

                            // 标记拖动动作
                            isDragging = true
                        }

                        MotionEvent.ACTION_UP -> {
                            // 手指抬起时，判断是否为拖动，若不是，则是点击
                            if (!isDragging) {
                                // 处理点击
                                v.performClick()
                            }
                        }
                    }
                    return true
                }
            })
        }
        FloatGiftManager.start(this)
    }

    private fun initRemoteVideoCloseView() {
        mBinding.ivRemoteCloseVideoHeader.loadAvatar(MMKVDataRep.userInfo.avatar.buildImageUrl())
    }

    private fun setAnchorInfoBgHeight() {
        // 获取屏幕的高度
        val displayMetrics = DisplayMetrics()
        windowManager.defaultDisplay.getMetrics(displayMetrics)
        val screenHeight = displayMetrics.heightPixels

        // 计算屏幕高度的69%
        val targetHeight = (screenHeight * 0.69).toInt()

        // 设置图片的高度
        val layoutParams = mBinding.anchorImage.layoutParams
        layoutParams.height = targetHeight
        mBinding.anchorImage.layoutParams = layoutParams
    }

    private fun initViewShow() {
        if (isIncomingCall) {
            mBinding.topAnchorInfo.makeGone()
            mBinding.flClose.makeGone()
            mBinding.flPlatform.makeGone()
//            mBinding.switchCamera.makeGone()
            mBinding.anchorListen.makeVisible()
            mBinding.placeholderBg.makeVisible()
            mBinding.tvConnecting.makeGone()
        } else {
//            mBinding.switchCamera.makeVisible()
            mBinding.anchorListen.makeGone()
            mBinding.placeholderBg.makeVisible()
            mBinding.tvConnecting.makeVisible()
        }
    }

    private fun realCalling() {
        anchorInfo?.id?.let {
            if (it.isNotEmpty()) {
                mViewModel.genVideoChannel(
                    it, callType = source
                )
            } else {
                ToastUtil.show(getString(R.string.tip_no_anchor))
                exceptionFinish()
            }
        }
    }

    override fun initData() {
        anchorInfo = intent.getStringExtra(Constants.INTENT_PARAM_KEY_ANCHOR_INFO)?.fromJson<UserBean>()
        anchorInfo?.id?.let {
            anchorViewModel.fetchAnchorDetail(it)
        }
        refreshAnchorInfoView(anchorInfo)
        initRemoteVideoCloseView()
        setupFlowListener()
        mediaPlayer = AppUtil.startRing(this@AnchorVideoActivity)
        countDownAnswer(false)
        LogX.i("$TAG, 是否为aib通话: $isIncomingCall")
        if (!isIncomingCall) {
            initializeAndPreview()
            realCalling()
        }
    }

    private fun setupFlowListener() {
        FlowBus.with<String>(Constants.PUSH_TYPE_BLACK_ANCHOR).register(this) {
            if (anchorInfo?.id == it) {
                finishCall()
                finish()
            }
        }

        FlowBus.with<RemoteCallRefuseExtend>(Constants.PushCmd.PUSH_CMD_REMOTE_CALL_REFUSE).register(this) {
            if (channelId == it.callID.toString()) {
                if (it.reasonType == "1") {
                    ToastUtil.show(getString(R.string.anchor_status_busy))
                } else if (it.reasonType == "8") {
                    ToastUtil.show(getString(R.string.anchor_status_busy))
                } else {
                    ToastUtil.show(getString(R.string.tip_video_canceled))
                }
                finishCall()
                finish()
            }
        }

        FlowBus.with<String>(Constants.PUSH_TYPE_ANCHOR_REJECT).register(this@AnchorVideoActivity) {
            if (it == channelId) {//收到主播拒絕 關閉也面
                if (callRejectDialog == null) {
                    callRejectDialog = showNormalNewPopup(
                        this@AnchorVideoActivity,
                        R.mipmap.ic_dialog_call,
                        title = getString(R.string.title_call_failed),
                        content = getString(R.string.tip_video_canceled),
                        btnSure = getString(R.string.string_ok),
                        mainColor = R.color.color_EC12E2,
                        dismissOnBackPressed = false,
                        dismissOnTouchOutside = false,
                        block = {
                            anchorInfo?.apply {
                                RongMessageUtil.insertOutgoingVideoMessage(
                                    id,
                                    getString(R.string.message_call_cancel)
                                )
                            }
                            finish()
                        },
                    ) { }
                } else {
                    callRejectDialog?.show()
                }

            }
        }

        FlowBus.with<String>(Constants.PUSH_TYPE_ANCHOR_CANCEL).register(this@AnchorVideoActivity) {
            if (it == channelId) {//收到主播拒絕 關閉也面
                val tipStr = getString(R.string.message_call_cancel)
                ToastUtil.show(tipStr)
                anchorInfo?.apply {
                    RongMessageUtil.insertOutgoingVideoMessage(id, tipStr)
                }
                finish()
            }
        }

        FlowBus.with<ChatTipBean>(Constants.PUSH_TYPE_TEXT_MESSAGE)
            .register(this@AnchorVideoActivity) {
                if (it.id == anchorUid.toString()) {
                    appendList(
                        mutableListOf(
                            VideoMessageBean(
                                name = anchorInfo?.nickname ?: "", message = it.content
                            )
                        )
                    )
                }
            }

        FlowBus.with<MikChatAskGiftMessage>(Constants.PUSH_TYPE_ANCHOR_REQUEST_GIFT)
            .register(this@AnchorVideoActivity) {
                appendList(
                    mutableListOf(
                        VideoMessageBean(
                            name = it.showName,
                            giftImage = it.icon,
                            giftId = it.id,
                            giftPrice = it.coin.toInt(),
                            isAsk = true
                        )
                    )
                )
            }
        FlowBus.with<Boolean>(Constants.PAYMENT_SUCCESS).register(this) { //支付成功
            if (it) {
                //充值成功 隐藏充值提醒
                mBinding.rechargeFl.makeGone()
                rechargeCountDownJob?.cancel()
                rechargeCountDownJob = null
            }
        }
    }

    private fun releaseRing() {
        mediaPlayer?.release()
        mediaPlayer = null
    }

    private fun countDownAnswer(isUserClick: Boolean, maxTime: Int = VIDEO_DEFAULT_EXIT_TIME) {
        //30s倒计时 没有接通的话 自动退出
        var count = maxTime
        answerCountDownJob =
            mViewModel.countDownCoroutines(maxTime, onTick = { time ->
                count--
            }, onFinish = {
                //倒计时结束并且无人接通 上报给服务端频道号 并关闭页面
                if (count <= 0 && anchorUid <= 0) {
                    mViewModel.aivRefuse(recordId, if (isNeedBal) 3 else 1, 1)
//                    channelId?.let {
//                        mViewModel.videoMissedCall(it)
//                    }
                    if (isUserClick) {
                        val tipStr = getString(R.string.tip_no_answer)
                        ToastUtil.show(tipStr)
                        anchorInfo?.apply {
                            RongMessageUtil.insertOutgoingVideoMessage(id, tipStr)
                        }
                    }
                    sendCallInviteCancelMessage(Constants.WebSocketParamValue.CALL_INVITE_CANCEL_REASON_TIMEOUT_CANCEL)
                    finishCall()
                    finish()
                }
            })
    }

    private fun countDownJoinChannel(maxTime: Int = 10) {
        //30s倒计时 没有接通的话 自动退出
        var count = maxTime
        joinChannelCountDownJob =
            mViewModel.countDownCoroutines(maxTime, onTick = { time ->
                count--
            }, onFinish = {
                if (count <= 0 && anchorUid <= 0) {
                    anchorInfo?.apply {
                        RongMessageUtil.insertOutgoingVideoMessage(id, getString(R.string.message_call_cancel))
                    }
                    ToastUtil.show(getString(R.string.join_channel_failed))
                    sendCallInviteCancelMessage(Constants.WebSocketParamValue.CALL_INVITE_CANCEL_REASON_USER_CANCEL)
                    finishCall()
                    finish()
                }
            })
    }

    private fun refreshAnchorInfoView(anchorInfo: UserBean?) {
        anchorInfo?.run {
            mBinding.run {
                anchorNameCenter.text = nickname
                anchorNameTop.text = nickname
                val showAvatar = avatar.buildImageUrl()
                anchorHeaderTop.loadAvatar(showAvatar.buildImageUrl())
                anchorHeaderCenter.loadAvatar(showAvatar.buildImageUrl())
                anchorImage.loadAnchorImage(showAvatar.buildImageUrl(highQuality = true))
                anchorAge.text = "$age"
                anchorAgeTop.text = "$age"
                anchorCountry?.title?.takeIf { it.isNotEmpty() }?.let {
                    anchorCountryCenter.makeVisible()
                    anchorCountryTop.makeVisible()
                    anchorCountryCenter.text = it
                    anchorCountryTop.text = it
                }

                tvDiamondCenter.text =
                    String.format(
                        getString(R.string.label_diamond_every_min),
                        (anchorLevel?.discountPrice ?: Constants.VIDEO_DEFAULT_PRICE).toShowDiamond()
                    )
                defaultAnchorDiamond = (anchorLevel?.discountPrice ?: Constants.VIDEO_DEFAULT_PRICE)
                llVipPrice.makeVisible(anchorLevel?.discountPrice != anchorLevel?.minDiscountPrice)
                tvDiamondVipCenter.text = String.format(
                    getString(R.string.label_diamond_every_min),
                    (anchorLevel?.minDiscountPrice ?: Constants.VIDEO_DEFAULT_PRICE).toShowDiamond()
                )

                followed = showRelation == Constants.FOLLOW_FLAG_FOLLOWED
                setFollowView()
            }
        }
    }

    override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)
        val isCameraSelf = intent.getBooleanExtra("IsCameraSelf", false)
        if (anchorUid > 0) { // 正在拨打中
            remoteVideoCanvas?.let {
                removeFromParent(it)
                mBinding.localVideoViewContainer.removeAllViews()
                mBinding.localVideoViewContainer.addView(it.view)
            }

            localSelVideoCanvas?.let {
                removeFromParent(it)
                mBinding.remoteVideoViewContainer.removeAllViews()
                mBinding.remoteVideoViewContainer.addView(it.view)
            }
        } else {
            localSelVideoCanvas?.let {
                removeFromParent(it)
                mBinding.localVideoViewContainer.removeAllViews()
                mBinding.localVideoViewContainer.addView(it.view)
            }
        }
        CallFloatWindow.instance.dismiss()
    }

    override fun onResume() {
        super.onResume()
        AppUtil.canAutoPopupVideoCallingPage = false
    }

    private fun initActionView() {
//        mBinding.switchCamera.click {
//            mRtcEngine?.switchCamera()
//        }
        mBinding.callingSwitchCamera.click {
            mRtcEngine?.switchCamera()
        }
        mBinding.callingSwitchVideo.run {
            click {
                muteLocalVideoStream = !muteLocalVideoStream
                setImageResource(if (muteLocalVideoStream) R.mipmap.ic_video_view_close else R.mipmap.ic_video_view_open)
                mRtcEngine?.muteLocalVideoStream(muteLocalVideoStream)
                mBinding.viewHideRemote.makeVisible(muteLocalVideoStream)
                mBinding.ivRemoteCloseVideoHeader.makeVisible(muteLocalVideoStream)
            }
        }
        mBinding.callingSwitchAudio.apply {
            click {
                muteLocalAudioStream = !muteLocalAudioStream
                setImageResource(if (muteLocalAudioStream) R.mipmap.ic_video_audio_close else R.mipmap.ic_video_audio_open)
                mRtcEngine?.muteLocalAudioStream(muteLocalAudioStream)
            }
        }
        mBinding.cancelCall.click {
            showExitPopup(true)
        }
//        mBinding.zoomOut.click {
//            moveTaskToBack(false)
//        }
        mBinding.flPlatform.click {
//            XPopup.Builder(this).asCustom(PlatformRulePopup(this)).show()
            anchorInfo?.id?.let { string ->
                showReportPopup(this, string)
            }
        }
        mBinding.flClose.click {
            showExitPopup(false)
//            showReportBlockPopup(this, anchorInfo)
        }
//        mBinding.remoteVideoViewContainer.click {
//            isSelfPreview = checkIsSelfPreview()
//            switchVideoPreview()
//        }
        mBinding.localVideoViewContainer.click {
            hideSendMsg()
        }
        mBinding.giftSend.click {
            giftPopup = giftPopup?.show() ?: showGiftPopup(this, anchorInfo?.id) { gift ->
                giveGift(gift)
            }
        }
        mBinding.ivSendMessage.click {
            //非vip不能发消息
            if (!MMKVDataRep.userInfo.isVIP) {
                showMembershipSubscribePopup(this, PurchaseScene.VideoCall(anchorInfo?.id ?: ""))
                return@click
            }
            mBinding.bottomSendMsg.visibility = View.VISIBLE
            mBinding.editBtn.setText("")
            mBinding.editBtn.requestFocus()
            SoftInputUtil.showSoftInput(mBinding.editBtn)
        }
        mBinding.inputPanelSendBtn.click {
            if (mBinding.editBtn.text?.isNotEmpty() == true) {
                anchorInfo?.id.let {
                    val content = mBinding.editBtn.text.toString().trim()
                    mBinding.editBtn.setText("")
                    val conversationType: Conversation.ConversationType =
                        Conversation.ConversationType.PRIVATE
                    val messageContent = TextMessage.obtain(content)
                    val message = Message.obtain(it, conversationType, messageContent)
                    message.isCanIncludeExpansion = true
                    RongIMClient.getInstance().sendMessage(message, null, null, object : IRongCallback.ISendMessageCallback {
                        override fun onAttached(message: Message?) {
                        }

                        override fun onSuccess(message: Message) {
                            appendList(
                                mutableListOf(
                                    VideoMessageBean(
                                        name = getString(R.string.label_me), message = content
                                    )
                                )
                            )
                            hideSendMsg()
                        }

                        override fun onError(message: Message?, errorCode: RongIMClient.ErrorCode?) {
                            ToastUtil.show(getString(R.string.message_send_failed))
                        }

                    })
                }
            }
        }

        val softInputUtil = SoftInputUtil()
        softInputUtil.attachSoftInput(mBinding.bottomSendMsg) { isSoftInputShow, _, viewOffset ->
            if (isSoftInputShow) {
                mBinding.bottomSendMsg.translationY -= viewOffset
            } else {
                mBinding.bottomSendMsg.translationY = 0f
                mBinding.bottomSendMsg.requestLayout()
            }
        }
        mBinding.btnRecharge.click {
            showDiamondRechargePopup(
                this,
                true,
                purchaseScene = PurchaseScene.VideoCall(anchorInfo?.id ?: "")
            ) {}
        }

        mBinding.anchorListen.click {
            if (isNeedBal) {
                showDiamondRechargeDialog()
            } else {
                AppUtil.requestVideoPermission(this, onGrantedCallBack = {
                    mBinding.videoCallAction.makeGone()
                    if (isIncomingCall) {
                        mBinding.tvConnecting.makeVisible()
                    }
                    answerEndTipStr = getString(R.string.label_connect_timeout)
                    initializeAndPreview()
                    realCalling()
                }) {
                    exceptionFinish()
                }
            }
        }

        mBinding.llTopFollow.click {
            followAction()
        }
        mBinding.llFollow.click {
            followAction()
        }
    }

    private fun showDiamondRechargeDialog() {
        if (diamondRechargePopup == null) {
            diamondRechargePopup = showDiamondRechargePopup(
                this, true, purchaseScene = PurchaseScene.VirtualVideo(
                    anchorId = anchorInfo?.id ?: "", videoId = anchorInfo?.virVideoId
                )
            ) { }
        } else {
            diamondRechargePopup?.show()
        }
    }

    private fun followAction() {
        if (followed) {
            followed = false
            setFollowView()
            anchorInfo?.id?.let {
                anchorViewModel.anchorUnfollow(it)
            }
        } else {
            followed = true
            setFollowView()
            anchorInfo?.id?.let {
                anchorViewModel.anchorFollow(it)
            }
        }
    }

    fun giveGift(gift: GiftItemBean) {
        mViewModel.giftGive(anchorId = anchorInfo?.id ?: "0", giftBean = gift, channelId?.toInt() ?: 0)
    }

    private fun appendList(messages: MutableList<VideoMessageBean>) {
        mBinding.messageList.append<VideoMessageItem>(messages) { data ->
            videoMessageBean = data as VideoMessageBean
        }
        mBinding.messageList.smoothScrollToPosition(mBinding.messageList.dslAdapter.itemCount - 1)
    }

    private fun hideSendMsg() {
        mBinding.bottomSendMsg.visibility = View.GONE
        SoftInputUtil.hideSoftInput(mBinding.editBtn)
    }

    //判断主视图是否是自己
    private fun checkIsSelfPreview(): Boolean {
        var self = false
        localSelVideoCanvas?.run {
            val group = view.parent as ViewGroup
            if (group == mBinding.localVideoViewContainer) {
                self = true
            }
        }
        return self
    }

    override fun initViewStates() {
        mViewModel.viewStates.let { states ->
            states.observeState(this, AnchorVideoPageState::anchorVideoStatus) {
                when (it) {
                    is AnchorVideoStatus.GenVideoChannelSuccess -> {
                        it.channelInfo?.let { info ->
                            channelId = info.channelId
                            //频道创建成功 取消默认倒计时 重新设置服务端配置的等待倒计时
                            cancelAnswerCountDown()
                            val tmpCountDownTime = if (info.waitDuration > 0) info.waitDuration else VIDEO_DEFAULT_EXIT_TIME
                            countDownAnswer(true, tmpCountDownTime)
                            /**
                             * token 获取成功加入频道
                             * 如果使用临时 Token 和频道名加入频道，uid 为 0 表示引擎内部随机生成用户名 成功后会触发 onJoinChannelSuccess 回调
                             */
                            val userCode = if (MMKVDataRep.userInfo.id.isNotEmpty()) {
                                MMKVDataRep.userInfo.id.toInt()
                            } else {
                                0
                            }
                            val joinWaitTime = if (info.joinChannelWaitTime > 0) info.joinChannelWaitTime else 10
                            countDownJoinChannel(joinWaitTime)
                            LogX.i("$TAG,  创建房间成功，房间ID: ${info.channelId} ， 用户ID:  $userCode ,声网RTCtoken： ${info.rtcToken}")
                            val joinCode = mRtcEngine?.joinChannel(
                                info.rtcToken, info.channelId, userCode, createChannelMediaOptions()
                            )
                            LogX.i("用户加入房间 $joinCode")
                            if (joinCode == 0) { //加入房间失败
                                LogX.i("$TAG, 用户加入房间失败，错误码： $joinCode")
                            } else {
                                LogX.i("$TAG, 用户加入房间成功")
                                sendCallHangUpMessage(Constants.WebSocketParamValue.CALL_INVITE_CANCEL_REASON_JOIN_FAILED)
                            }
                            //加入房间等待固定时间后 如果没有进入 直接退出页面
                        }
                    }

                    is AnchorVideoStatus.GenVideoChannelFailed -> {
                        LogX.e("$TAG, 创建房间失败 : ${it.message}")
                        exceptionFinish()
                    }

                    is AnchorVideoStatus.GetAvailableTimeSuccess -> {
                        it.availableTimeBean.let { availableTime ->
                            DiamondChangeManager.updateDiamond(availableTime.userBalance) //更新用户钻石数
                            if (mBinding.rechargeFl.isVisible) {
                                return@let
                            }
                            if (availableTime.isBalanceNotEnough) {
                                if (availableTime.surplusDuration > 0) {
                                    //余额不足 提示充值
                                    mBinding.rechargeFl.visibility = View.VISIBLE
                                    var rechargeCount = availableTime.surplusDuration
                                    mBinding.customProgressView.startProgressAnimation(availableTime.surplusDuration * 1000.toLong())
                                    if (rechargeCountDownJob == null) {
                                        rechargeCountDownJob = mViewModel.countDownCoroutines(
                                            availableTime.surplusDuration,
                                            onTick = { time ->
                                                mBinding.rechargeCountTime.text = String.format(
                                                    getString(R.string.tip_call_end), time
                                                )
                                                rechargeCount = time
                                            },
                                            onFinish = {
                                                if (rechargeCount <= 0) {
                                                    sendCallHangUpMessage(Constants.WebSocketParamValue.CALL_HANG_UP_REASON_INSUFFICIENT_DIAMONDS)
                                                    ToastUtil.show(getString(R.string.tip_insufficient_diamonds_call_failed))
                                                    finishCall()
                                                    finish()
                                                }
                                            })
                                    }
                                } else {
                                    //剩余时间不足 立即关闭页面
                                    ToastUtil.show(getString(R.string.tip_insufficient_diamonds_call_failed))
                                    finishCall()
                                    finish()
                                }
                            }
                        }

                    }

                    is AnchorVideoStatus.GiftGiveSuccess -> {
//                        anchorInfo?.id?.let { id ->
//                            RongMessageUtil.sendGiftMessage(id, it.giftBean, channelId ?: "0")
//                        }
                        appendList(
                            mutableListOf(
                                VideoMessageBean(
                                    giftImage = it.giftBean.icon, giftCount = it.giveNum
                                )
                            )
                        )
                        GiftManager.getInstance(this).playGiftAnimation(this, this, it.giftBean.giftSvgaUrl)
                    }

                    else -> {}
                }
            }
        }
    }

    //设置follow btn ui
    private fun setFollowView() {
        if (followed) {
            val labelCancelFollow = getString(R.string.label_cancel_follow)
            mBinding.tvFollow.text = labelCancelFollow
            mBinding.tvTopFollow.text = labelCancelFollow
            mBinding.followedIv.setImageResource(R.mipmap.ic_call_unfollow)
            mBinding.ivTopFollow.setImageResource(R.mipmap.ic_call_top_unfollow)
            mBinding.llTopFollow.setBackgroundResource(R.drawable.shape_call_top_unfollow_btn_bg)
            mBinding.llFollow.setBackgroundResource(R.mipmap.bg_call_info_unfollow)
        } else {
            val labelFollow = getString(R.string.off_attention)
            mBinding.tvFollow.text = labelFollow
            mBinding.tvTopFollow.text = labelFollow
            mBinding.followedIv.setImageResource(R.mipmap.ic_follow)
            mBinding.ivTopFollow.setImageResource(R.mipmap.ic_call_top_follow)
            mBinding.llTopFollow.setBackgroundResource(R.drawable.shape_call_top_follow_btn_bg)
            mBinding.llFollow.setBackgroundResource(R.mipmap.bg_call_info_follow)
        }
    }

    override fun initViewEvents() {
        mViewModel.viewEvents.observeEvent(this) {
            when (it) {
                is BaseRequestEvent.ShowToast -> ToastUtil.show(it.message)
                else -> {}
            }
        }

        anchorViewModel.pageEvents.observeEvent(this) {
            when (it) {
                is SearchRequestEvent.AnchorFollowFailed -> {
                    ToastUtil.show(it.msg)
                    followed = false
                    setFollowView()
                }

                is SearchRequestEvent.AnchorUnFollowFailed -> {
                    ToastUtil.show(it.msg)
                    followed = true
                    setFollowView()
                }

                is SearchRequestEvent.GetAnchorDetailSuccess -> {
                    anchorInfo = it.userBean
                    refreshAnchorInfoView(it.userBean)
                    //用户信息有修改 更新数据IM用户信息
                    RongMessageUtil.refreshCacheUserInfo(it.userBean)
                }

                else -> {}
            }
        }
    }

    private var mIsExceptionFinish = false

    //接口异常退出
    private fun exceptionFinish() {
        mIsExceptionFinish = true
        finish()
    }

    /**
     * 初始化视频引擎显示本地视图
     */
    private fun initializeAndPreview() {
        LogX.i("$TAG, 开始初始化声网引擎")
        try {
            //log日志
            val logConfig = RtcEngineConfig.LogConfig()
            logConfig.level = io.agora.rtc2.Constants.LogLevel.getValue(io.agora.rtc2.Constants.LogLevel.LOG_LEVEL_INFO)
            logConfig.fileSizeInKB = 2048

            // 创建并初始化 RtcEngine
            val config = RtcEngineConfig()
            config.mContext = baseContext
            if (MMKVBaseDataRep.agoraRtcAppId != null && MMKVBaseDataRep.agoraRtcAppId!!.isNotEmpty()) {
                config.mAppId = MMKVBaseDataRep.agoraRtcAppId
            }
            config.mEventHandler = mRtcEventHandler
            config.mLogConfig = logConfig
            mRtcEngine = RtcEngine.create(config)
        } catch (e: Exception) {
            LogX.i("$TAG, 初始化声网引擎失败: ${e.message}")
            throw RuntimeException("Check the error.")
        }
        mRtcEngine?.apply {
            // 启用视频模块
            enableVideo()
            // 创建一个 SurfaceView 对象，并将其作为 FrameLayout 的子对象
            val surfaceView = SurfaceView(baseContext)
            mBinding.localVideoViewContainer.addView(surfaceView)
            // 将 SurfaceView 对象传入声网实时互动 SDK，设置本地视图
            localSelVideoCanvas = VideoCanvas(surfaceView, VideoCanvas.RENDER_MODE_HIDDEN, 0)
            setupLocalVideo(localSelVideoCanvas)
            // 开启本地预览
            startPreview()
            setBeautyEffectOptions(true, BeautyOptions())

            LogX.i("$TAG, 声网引擎初始化成功")
        }
    }

    private fun createChannelMediaOptions(): ChannelMediaOptions {
        // 创建 ChannelMediaOptions 对象，并进行配置
        return ChannelMediaOptions().apply {
            // 设置用户角色为 BROADCASTER (主播) 或 AUDIENCE (观众)
            clientRoleType = io.agora.rtc2.Constants.CLIENT_ROLE_BROADCASTER
            // 设置频道场景为 BROADCASTING (直播场景)
            channelProfile = io.agora.rtc2.Constants.CHANNEL_PROFILE_LIVE_BROADCASTING
            // 发布麦克风采集的音频
            publishMicrophoneTrack = true
            // 发布摄像头采集的视频
            publishCameraTrack = true
            // 自动订阅所有音频流
            autoSubscribeAudio = true
            // 自动订阅所有视频流
            autoSubscribeVideo = true
        }
    }

    private fun setupRemoteVideo(uid: Int) {
        LogX.i("$TAG, 设置远端视图: $uid , channelID:$channelId")
        val surfaceView = SurfaceView(baseContext)
        surfaceView.setZOrderMediaOverlay(true)
        mBinding.remoteVideoViewContainerFl.visibility = View.VISIBLE
        mBinding.remoteVideoViewContainer.addView(surfaceView)
        // 将 SurfaceView 对象传入声网实时互动 SDK，设置远端视图
        remoteVideoCanvas = VideoCanvas(surfaceView, VideoCanvas.RENDER_MODE_HIDDEN, uid)
        mRtcEngine?.setupRemoteVideo(remoteVideoCanvas)
    }

    private fun removeFromParent(canvas: VideoCanvas?): ViewGroup? {
        if (canvas != null) {
            val parent = canvas.view.parent
            if (parent != null) {
                val group = parent as ViewGroup
                group.removeView(canvas.view)
                return group
            }
        }
        return null
    }

    /**
     * 切换摄像头
     */
    private fun switchView(canvas: VideoCanvas) {
        val parent = removeFromParent(canvas)
        if (parent === mBinding.localVideoViewContainer) {
            if (canvas.view is SurfaceView) {
                (canvas.view as SurfaceView).setZOrderMediaOverlay(true)
            }
            mBinding.remoteVideoViewContainer.addView(canvas.view)
        } else if (parent === mBinding.remoteVideoViewContainer) {
            if (canvas.view is SurfaceView) {
                (canvas.view as SurfaceView).setZOrderMediaOverlay(false)
            }
            mBinding.localVideoViewContainer.addView(canvas.view)
        }
    }

    private fun switchVideoPreview() {
        remoteVideoCanvas?.let {
            switchView(it)
        }
        localSelVideoCanvas?.let {
            switchView(it)
        }
    }

    private val mRtcEventHandler: IRtcEngineEventHandler = object : IRtcEngineEventHandler() {
        // 成功加入频道回调
        override fun onJoinChannelSuccess(channel: String, uid: Int, elapsed: Int) {
            super.onJoinChannelSuccess(channel, uid, elapsed)
            LogX.e("$TAG, 用户加入频道成功 $channel $uid")
            joinChannelCountDownJob?.cancel()
            joinChannelCountDownJob = null
            //上报加入房间socket
            WebSocketMessageSender.sendCallJoinRoomMessage(channel.toInt())
        }

        override fun onConnectionStateChanged(state: Int, reason: Int) {
            super.onConnectionStateChanged(state, reason)
            LogX.e("$TAG, 声网连接状态发生变化了:state: $state , reason:$reason")
            //TODO 出现一个现象 主播端秒挂的话 回调是 5用户离开频道， 3用户被服务器禁止
            if (state == 5 && reason == 3) {
                sendCallHangUpMessage(Constants.WebSocketParamValue.CALL_HANG_UP_REASON_BANNED_BY_SERVER)
                finishCall()
                finish()
            }
        }

        // 远端用户或主播加入当前频道回调
        override fun onUserJoined(uid: Int, elapsed: Int) {
            // 当远端用户加入频道后，显示指定 uid 的远端视频流
            LogX.e("$TAG, 主播加入房间, ID: $uid, channelID: $channelId")
            isSelfPreview = false
            anchorUid = uid
            cancelAnswerCountDown()
            releaseRing()
            runOnUiThread {
                setupRemoteVideo(uid)
                switchVideoPreview()
                setupTimeView()
                updateActionView()

//                if (CallFloatWindow.instance.isShowing) {
//                    CallFloatWindow.instance.apply {
//                        show()
//                        update(anchorUid > 0, isSelfPreview, localSelVideoCanvas, remoteVideoCanvas)
//                    }
//                }
            }

            // 更新WebSocketManager中的心跳数据
            channelId?.let { channelId ->
                WebSocketManager.getInstance().updateHeartbeatData(channelId.toInt())
            }


        }

        // 远端用户或主播离开当前频道回调
        override fun onUserOffline(uid: Int, reason: Int) {
            LogX.i("$TAG, 主播离开房间，房间ID：$channelId，主播ID: $uid")
            super.onUserOffline(uid, reason)
            sendCallHangUpMessage(Constants.WebSocketParamValue.CALL_HANG_UP_REASON_HUNG_UP_BY_OTHER)
            runOnUiThread {
                ToastUtil.show(getString(R.string.tip_video_cancel))
                finishCall()
                finish()
            }
        }

        override fun onError(err: Int) {
            LogX.e("$TAG, 声网回调错误： error $err , 房间ID: $channelId")
            super.onError(err)
        }
    }

    private fun gotoCallSettlementActivity() {
        val intent = Intent(this@AnchorVideoActivity, AnchorRateStarActivity::class.java)
        intent.putExtra(Constants.INTENT_PARAM_KEY_CHANNEL_ID, channelId)
        intent.putExtra(Constants.INTENT_PARAM_KEY_ANCHOR_INFO, anchorInfo?.toJson())
        startActivity(intent)
    }

    private fun setupTimeView() {
        mBinding.videoTime.visibility = View.VISIBLE
        var isContinue = true
        videoTimeJob = CoroutineScope(Dispatchers.Main).launch {
            while (isActive) {
                if (timeString.isNotEmpty()) {
                    timeString.clear()
                }
                appendTimeStr(seconds / 3600, false)
                appendTimeStr((seconds % 3600) / 60, true)
                val secShow = seconds % 60
                val secStr = if (secShow > 9) secShow % 60 else "0$secShow"
                timeString.append(secStr)
                mBinding.videoTime.text = timeString
                if (CallFloatWindow.instance.isShowing) {
                    CallFloatWindow.instance.setTimeContent(timeString.toString())
                }
                seconds++

                if (seconds % 5 == 0) {
                    channelId?.let {
                        mViewModel.getAvailableTime(it)
                    }
                }
                //匹配进入30s倒计时 没有弹充值提示说明有钱扣费 还剩10s的时候弹窗提示
                if (source == Constants.VIDEO_SOURCE_MATCH && seconds >= 20 && mBinding.rechargeFl.isGone) {
                    if (isContinue) {
                        val content = String.format(
                            getString(R.string.popup_content_videoing_match_tip),
                            30 - seconds,
                            defaultAnchorDiamond.toShowDiamond()
                        )
                        if (matchTipDialog == null) {
                            matchTipDialog = showNormalNewPopup(
                                this@AnchorVideoActivity,
                                R.mipmap.ic_dialog_call,
                                title = getString(R.string.remind),
                                content = content,
                                btnSure = getString(R.string.popup_btn_continue),
                                btnCancel = getString(R.string.popup_btn_reject),
                                mainColor = R.color.color_EC12E2,
                                block = {
                                    isContinue = true
                                },
                            ) {
                                isContinue = false
                                ToastUtil.show(
                                    String.format(
                                        getString(R.string.video_left_time_tip), 30 - seconds
                                    )
                                )
                            }
                        } else {
                            matchTipDialog?.apply {
                                if (isShow) {
                                    setContent(content)
                                }
                            }
                        }

                        //第30s如果弹窗还在，说明用户没有操作 关闭弹窗 默认继续 并扣费
                        if (seconds == 30) {
                            matchTipDialog?.dismiss()
                        }
                    } else {
                        //用户拒绝了， 第30s关闭通话
                        if (seconds == 30) {
                            finishCall()
                            finish()
                        }
                    }
                }
                delay(1000) // 每1秒延迟
            }
        }
    }

    private fun appendTimeStr(time: Int, always: Boolean) {
        if (time > 9) {
            timeString.append("$time:")
        } else if (time > 0) {
            timeString.append("0$time:")
        } else {
            if (always) {
                timeString.append("00:")
            }
        }
    }

    private fun updateActionView() {
        mBinding.callAnchorInfo.visibility = View.GONE
        mBinding.topAnchorInfo.makeVisible()
        mBinding.flClose.makeVisible()
        mBinding.flPlatform.makeVisible()
        mBinding.videoCallingAction.visibility = View.VISIBLE
        mBinding.giftSend.visibility = View.VISIBLE
        mBinding.messageList.visibility = View.VISIBLE
        mBinding.placeholderBg.makeGone()
    }

    private fun showExitPopup(isUserCancel: Boolean = false) {
        if (exitTipPopupView == null) {
            exitTipPopupView = showNormalNewPopup(
                this,
                R.mipmap.ic_dialog_call,
                title = getString(R.string.btn_hangup),
                content = getString(R.string.dialog_content_hang_up),
                btnSure = getString(R.string.btn_connecting),
                btnCancel = getString(R.string.btn_hangup),
                mainColor = R.color.color_EC12E2,
                cancelBlock = {
                    if (isIncomingCall && anchorUid <= 0) { //没有接通 用户自己挂断
                        //假aib需要付费
                        LogX.e("$TAG, 推送的aib，没有接通，用户挂断")
                        mViewModel.aivRefuse(recordId, if (isNeedBal) 3 else 1, 0)
                    }
                    if (anchorUid > 0) { //接通主动挂断
                        LogX.e("$TAG, 通话接通了， 用户主动挂断")
                        sendCallHangUpMessage(Constants.WebSocketParamValue.CALL_HANG_UP_REASON_USER_HUNG_UP)

                    } else {//未接通主动挂断
                        LogX.e("$TAG, 通话未接通， 用户主动挂断")
                        sendCallInviteCancelMessage(Constants.WebSocketParamValue.CALL_INVITE_CANCEL_REASON_USER_CANCEL)
                    }

                    finishCall()
                    finish()
                })
        } else {
            XPopup.Builder(this).asCustom(exitTipPopupView).show()
        }
    }

    private fun sendCallInviteCancelMessage(reasonType: Int) {
        WebSocketMessageSender.sendCallInviteCancelMessage(anchorInfo?.id?.toInt() ?: 0, channelId?.toInt() ?: 0, reasonType)
    }

    private fun sendCallHangUpMessage(reasonType: Int) {
        WebSocketMessageSender.sendCallHangUpMessage(0, 0, channelId?.toInt() ?: 0, reasonType)
    }

//    private fun showFloatWindow() {
//        moveTaskToBack(true)
//        CallFloatWindow.instance.apply {
//            show()
//            update(anchorUid > 0, isSelfPreview, localSelVideoCanvas, remoteVideoCanvas)
//            if (anchorUid <= 0) {
//                setTimeContent(getString(R.string.tip_wait_call))
//            }
//        }
//    }

    override fun onKeyDown(keyCode: Int, event: KeyEvent?): Boolean {
        // 是否触发按键为back键
        return if (keyCode == KeyEvent.KEYCODE_BACK) {
            backEvent()
            true
        } else {
            // 如果不是back键正常响应
            super.onKeyDown(keyCode, event)
        }
    }

    private fun backEvent() {
        if (isIncomingCall && anchorUid <= 0) {
            showExitPopup(true)
        } else {
            if (mBinding.bottomSendMsg.isVisible) {
                hideSendMsg()
            } else {
                showExitPopup()
            }
        }
    }

    private fun cancelAnswerCountDown() {
        answerCountDownJob?.cancel()
        answerCountDownJob = null
    }

    private var isFinishCalled: Boolean = false
    private fun finishCall() {
        if (isFinishCalled) {
            return
        }
        isFinishCalled = true
        mRtcEngine?.apply {
            stopChannelMediaRelay()
            leaveChannel()
            stopPreview()
        }
        if (anchorUid > 0) { //说明有通话 显示通话总结
            gotoCallSettlementActivity()
            if (timeString.isEmpty()) {
                timeString.append("00:01")
            }
            anchorInfo?.apply {
                if (isIncomingCall) {
                    RongMessageUtil.insertIncomingVideoMessage(id, timeString.toString())
                } else {
                    RongMessageUtil.insertOutgoingVideoMessage(id, timeString.toString())
                }
            }
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        AppUtil.canAutoPopupVideoCallingPage = true //reset
        videoTimeJob?.cancel() // 停止计时器
        cancelAnswerCountDown()
        mRtcEngine?.apply {
            stopPreview() // 停止本地视频预览
            leaveChannel()// 离开频道
        }
        callRejectDialog?.dismiss()
        callRejectDialog = null

        matchTipDialog?.dismiss()
        matchTipDialog = null

        mRtcEngine = null
        RtcEngine.destroy()// 销毁引擎
        releaseRing()
        CallFloatWindow.instance.dismiss()
    }

}